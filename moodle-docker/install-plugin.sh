#!/bin/bash

# Moodle Plugin Installation Script
# Usage: ./install-plugin.sh <plugin_type> <plugin_name> <download_url>
# Example: ./install-plugin.sh mod generico https://example.com/plugin.zip

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if correct number of arguments provided
if [ $# -ne 3 ]; then
    print_error "Usage: $0 <plugin_type> <plugin_name> <download_url>"
    print_error "Example: $0 mod generico https://example.com/plugin.zip"
    print_error ""
    print_error "Common plugin types:"
    print_error "  mod - Activity modules"
    print_error "  blocks - Block plugins"
    print_error "  theme - Themes"
    print_error "  filter - Content filters"
    print_error "  local - Local plugins"
    exit 1
fi

PLUGIN_TYPE=$1
PLUGIN_NAME=$2
DOWNLOAD_URL=$3

# Validate plugin type
VALID_TYPES=("mod" "blocks" "theme" "filter" "local" "auth" "enrol" "admin/tool")
if [[ ! " ${VALID_TYPES[@]} " =~ " ${PLUGIN_TYPE} " ]]; then
    print_error "Invalid plugin type: $PLUGIN_TYPE"
    print_error "Valid types: ${VALID_TYPES[*]}"
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null; then
    print_error "docker-compose is not installed or not in PATH"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "docker-compose.yml" ]; then
    print_error "docker-compose.yml not found. Please run this script from the moodle-docker directory."
    exit 1
fi

print_status "Installing plugin: $PLUGIN_NAME (type: $PLUGIN_TYPE)"

# Create temporary directory
TEMP_DIR=$(mktemp -d)
trap "rm -rf $TEMP_DIR" EXIT

# Download plugin
print_status "Downloading plugin from: $DOWNLOAD_URL"
cd "$TEMP_DIR"
if ! curl -L -o plugin.zip "$DOWNLOAD_URL"; then
    print_error "Failed to download plugin"
    exit 1
fi

# Extract plugin
print_status "Extracting plugin..."
if ! unzip -q plugin.zip; then
    print_error "Failed to extract plugin"
    exit 1
fi

# Find the extracted directory (assuming it's the only directory)
EXTRACTED_DIR=$(find . -maxdepth 1 -type d ! -name "." | head -n 1)
if [ -z "$EXTRACTED_DIR" ]; then
    print_error "Could not find extracted plugin directory"
    exit 1
fi

# Go back to the original directory
cd - > /dev/null

# Stop Docker containers
print_status "Stopping Docker containers..."
docker-compose down

# Create plugin directory path
PLUGIN_PATH="moodle/$PLUGIN_TYPE/$PLUGIN_NAME"

# Check if plugin already exists
if [ -d "$PLUGIN_PATH" ]; then
    print_warning "Plugin directory already exists: $PLUGIN_PATH"
    read -p "Do you want to overwrite it? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Installation cancelled"
        exit 0
    fi
    rm -rf "$PLUGIN_PATH"
fi

# Copy plugin files
print_status "Installing plugin to: $PLUGIN_PATH"
mkdir -p "$(dirname "$PLUGIN_PATH")"
cp -r "$TEMP_DIR/$EXTRACTED_DIR" "$PLUGIN_PATH"

# Set proper permissions
print_status "Setting permissions..."
chmod -R 755 "$PLUGIN_PATH"

# Start Docker containers
print_status "Starting Docker containers..."
docker-compose up -d

# Wait for containers to be ready
print_status "Waiting for containers to be ready..."
sleep 10

print_status "Plugin installation completed!"
print_warning "Next steps:"
echo "1. Open your Moodle site in a browser"
echo "2. Login as admin"
echo "3. Go to Site Administration → Notifications"
echo "4. Follow the installation wizard to complete the plugin setup"
echo ""
print_status "Plugin installed at: $PLUGIN_PATH"
