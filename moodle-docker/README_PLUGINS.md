# Moodle Plugin Installation Guide

## Current Plugin Status

Your Moodle 5.0 installation already includes several built-in plugins:

### ✅ Already Available
- **H5P Activity Module** (`mod/h5pactivity`) - Interactive content
- **H5P Core Libraries** (`h5p/`) - H5P framework
- **H5P Editor Integration** (`lib/editor/tiny/plugins/h5p/`) - Content creation
- **BigBlueButton** (`mod/bigbluebuttonbn`) - Video conferencing
- **Display H5P Filter** (`filter/displayh5p/`) - H5P content display

## Plugin Installation Methods

### Method 1: Via Admin Interface (Recommended)
1. Login to Moodle as admin
2. Go to Site Administration → Plugins → Install plugins
3. Upload plugin ZIP file
4. Follow installation wizard

### Method 2: Manual Installation (Docker)
```bash
# Stop containers
docker-compose down

# Add plugin to appropriate directory
cd moodle/[plugin_type]/
# Extract plugin files here

# Restart containers
docker-compose up -d

# Complete installation via web interface
```

## Plugin Directory Structure

- `mod/` - Activity modules (assignments, quizzes, etc.)
- `blocks/` - Sidebar blocks
- `theme/` - Themes and appearance
- `auth/` - Authentication methods
- `filter/` - Content filters
- `admin/tool/` - Admin tools
- `local/` - Custom local plugins

## Recommended Plugins

### Content Creation
- **Generico** - Template-based activities
- **Interactive Video** - Video with interactions
- **PDF Annotation** - Annotate PDF files

### Assessment
- **Questionnaire** - Advanced surveys
- **Student Quiz** - Student-created quizzes
- **Checklist** - Progress tracking

### Communication
- **Jitsi Meet** - Alternative video conferencing
- **Forum Plus** - Enhanced discussion forums

### Analytics
- **Configurable Reports** - Custom reporting
- **Learning Analytics** - Progress tracking
- **Completion Progress** - Visual progress

## Installation Example: Generico Plugin

1. Download from: https://moodle.org/plugins/mod_generico
2. Extract to: `moodle/mod/generico/`
3. Set permissions: `chmod -R 755 moodle/mod/generico/`
4. Restart containers: `docker-compose restart`
5. Complete installation via Moodle admin interface

## Security Considerations

- Only install plugins from trusted sources
- Always backup before installing plugins
- Test plugins in development environment first
- Keep plugins updated for security

## Troubleshooting

### Common Issues
1. **Permission errors**: Check file permissions (755 for directories, 644 for files)
2. **Database errors**: Ensure database is accessible
3. **Version conflicts**: Check plugin compatibility with Moodle 5.0

### Getting Help
- Check plugin documentation
- Moodle community forums
- Plugin developer support pages
